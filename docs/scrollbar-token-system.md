# Global Scrollbar Token System

## Overview

A comprehensive scrollbar token system has been implemented across the entire Snap application to provide consistent scrollbar styling throughout all components. This system uses CSS custom properties (tokens) to ensure uniform appearance and easy maintenance.

## Scrollbar Specifications

The global scrollbar system implements the following specifications:

### Light Mode
- **Thumb (normal)**: `#D9DDEB`
- **Thumb (hover)**: `#E9EBF2`

### Dark Mode
- **Thumb (normal)**: `#2F3341`
- **Thumb (hover)**: `#3F455B`

### Universal Properties
- **Track**: `transparent`
- **Corner**: `transparent`
- **Width**: `8px` (vertical scrollbar)
- **Height**: `8px` (horizontal scrollbar)

## CSS Tokens

The following tokens are available in `css/tokens.css`:

```css
/* Scrollbar Tokens */
--scrollbar-width: 8px;
--scrollbar-height: 8px;
--scrollbar-thumb-light: #D9DDEB;
--scrollbar-thumb-light-hover: #E9EBF2;
--scrollbar-thumb-dark: #2F3341;
--scrollbar-thumb-dark-hover: #3F455B;
--scrollbar-track: transparent;
--scrollbar-corner: transparent;
```

## Utility Classes

### Standard Scrollbar
Apply the `.snap-scrollbar` class to any element that needs standard scrollbar styling:

```css
.my-scrollable-element {
    overflow-y: auto;
}
```

```html
<div class="my-scrollable-element snap-scrollbar">
    <!-- scrollable content -->
</div>
```

### Hidden Scrollbar
Use the `.snap-scrollbar-hidden` class to completely hide scrollbars:

```css
.my-element {
    overflow: auto;
}
```

```html
<div class="my-element snap-scrollbar-hidden">
    <!-- content with hidden scrollbars -->
</div>
```

## Components Updated

The following components have been updated to use the global scrollbar tokens:

### Data Grid (`components/data-grid/snap-grid.css`)
- Grid viewport (`.snap-grid-viewport`)
- Column list (`.column-list`)
- Filter checkbox list (`.filter-checkbox-list`)
- Dropdown menus (`.snap-dropdown .dropdown-menu`)

### Datepicker (`components/datepicker/snap-datepicker.css`)
- Month dropdown (`.snap-datepicker-dropdown-month .dropdown-menu`)
- Year dropdown (`.snap-datepicker-dropdown-year .dropdown-menu`)

### Main Application (`snapapp.css`)
- General dropdown lists (`.snap-dropdown .dropdown-list`)
- Sales scrollable content (`.sales-scrollable-content`) - preserves overlay behavior
- Main page body scrollbar
- Dark mode variants for all components

### Virtual List (`components/virtual-list/virtual-list-styles.css`)
- Virtual list viewport (`.virtual-list-viewport`)

### Charts (`components/charts/snap-charts.css`)
- Charts maintain their existing behavior (hidden scrollbars where intended)
- Custom scrollbars use the token system where applicable

## Implementation Details

### Automatic Dark Mode Support
All scrollbar styling automatically adapts to dark mode using the `[data-theme="dark"]` selector:

```css
[data-theme="dark"] .my-element {
    scrollbar-color: var(--scrollbar-thumb-dark) var(--scrollbar-track);
}

[data-theme="dark"] .my-element::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb-dark);
}

[data-theme="dark"] .my-element::-webkit-scrollbar-thumb:hover {
    background: var(--scrollbar-thumb-dark-hover);
}
```

### Cross-Browser Compatibility
The system supports both WebKit browsers (Chrome, Safari, Edge) and Firefox:

- **WebKit**: Uses `::-webkit-scrollbar` pseudo-elements
- **Firefox**: Uses `scrollbar-width` and `scrollbar-color` properties

### Special Behaviors Preserved
- **Sales scrollable content**: Maintains overlay behavior (hidden by default, visible on hover)
- **Charts**: Maintains existing hidden scrollbar behavior where intended
- **Main page body**: Maintains dynamic show/hide behavior on hover

## Usage Guidelines

1. **For new components**: Add the `.snap-scrollbar` class to scrollable elements
2. **For existing components**: Use the global tokens directly in CSS
3. **For hidden scrollbars**: Use the `.snap-scrollbar-hidden` class
4. **For custom behavior**: Reference the tokens while maintaining special functionality

## Benefits

- **Consistency**: Uniform scrollbar appearance across all components
- **Maintainability**: Single source of truth for scrollbar styling
- **Theme Support**: Automatic light/dark mode adaptation
- **Performance**: Optimized CSS with minimal redundancy
- **Accessibility**: Consistent interaction patterns for users
